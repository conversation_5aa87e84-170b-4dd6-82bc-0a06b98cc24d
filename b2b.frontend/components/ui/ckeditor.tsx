'use client';

import React, { useState } from 'react';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor, Paragraph, Bold, Italic, Underline, Link, List, Heading, Alignment, Table, TableToolbar, SourceEditing } from 'ckeditor5';


interface CkeditorProps {
    content?: string;
    onChange?: (content: string) => void;
    placeholder?: string;
    editable?: boolean;
}

export default function CkeditorEditor({
    content = '',
    onChange,
    placeholder = 'İçeriğinizi yazın...',
    editable = true,
}: CkeditorProps) {
    const [editorData, setEditorData] = useState(content);

    return (
        <div className="border rounded-lg overflow-hidden">
            <CKEditor
                editor={ClassicEditor}
                data={editorData}
                disabled={!editable}
                config={{
                    licenseKey: 'eyJhbGciOiJFUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************.eWUar_CVfhCvnfQ-mbUbmjxaIXOE6erVbgHfRGAp4SWh5PU51FkyL7Eu3_bmNGpNNqnSPkDaX27dFWX233oMMQ',
                    placeholder,
                    plugins: [
                        Paragraph,
                        Bold,
                        Italic,
                        Underline,
                        Link,
                        List,
                        Heading,
                        Alignment,
                        Table,
                        TableToolbar,
                        SourceEditing],
                    toolbar: {
                        items: [
                            'heading',
                            '|',
                            'bold',
                            'italic',
                            'underline',
                            'link',
                            'bulletedList',
                            'numberedList',
                            '|',
                            'alignment',
                            '|',
                            'insertTable',
                            'undo',
                            'redo',
                            '|',
                            'sourceEditing', // Butonu toolbar'a ekliyoruz
                        ]
                    }
                }}
                onChange={(_, editor) => {
                    const data = editor.getData(); // HTML içerik burada
                    setEditorData(data);
                    onChange?.(data);
                }}
            />
        </div>
    );
}