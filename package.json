{"name": "b2b", "scripts": {"dbupdate": "dotnet ef database update --project B2B.Backend/Infrastructure --startup-project B2B.Backend/PanelApi", "panel-api": "dotnet run --project B2B.Backend/PanelApi", "web-api": "dotnet run --project B2B.Backend/WebApi", "web": "npm --prefix b2b.frontend run dev", "start": "concurrently --names \"Frontend,PanelAPI,WebAPI,MediaAPI,MailAPI,APIGateway\" --prefix-colors \"cyan,green,blue,yellow,magenta,red\" \"npm --prefix b2b.frontend run dev\" \"dotnet run --project B2B.Backend/PanelApi\" \"dotnet run --project B2B.Backend/WebApi\" \"dotnet run --project B2B.Backend/MediaAPI\" \"dotnet run --project B2B.Backend/MailAPI\" \"dotnet run --project B2B.Backend/ApiGateway\"", "media-api": "dotnet run --project B2B.Backend/MediaAPI", "mail-api": "dotnet run --project B2B.Backend/MailAPI", "api-gateway": "dotnet run --project B2B.Backend/ApiGateway", "all": "concurrently --names \"PanelAPI,WebAPI,MediaAPI,MailAPI,APIGateway\" --prefix-colors \"green,blue,yellow,magenta,red\" \"dotnet run --project B2B.Backend/PanelApi\" \"dotnet run --project B2B.Backend/WebApi\" \"dotnet run --project B2B.Backend/MediaAPI\" \"dotnet run --project B2B.Backend/MailAPI\" \"dotnet run --project B2B.Backend/ApiGateway\"", "api": "dotnet run --project B2B.Backend/PanelApi", "backend": "concurrently --names \"PanelAPI,MediaAPI,MailAPI,APIGateway,PanelWeb\" --prefix-colors \"blue,green,magenta,yellow,red\" \"dotnet run --project B2B.Backend/PanelApi\" \"dotnet run --project B2B.Backend/MediaAPI\" \"dotnet run --project B2B.Backend/MailAPI\" \"dotnet run --project B2B.Backend/ApiGateway\" \"npm --prefix b2b.frontend run dev\" ", "frontend": "concurrently --names \"<PERSON><PERSON><PERSON>,B2<PERSON><PERSON><PERSON>\" --prefix-colors \"blue,red\" \"dotnet run --project B2B.Backend/WebApi\" \"npm --prefix themes/vineta run dev\""}, "devDependencies": {"concurrently": "^8.2.0"}, "dependencies": {"@ckeditor/ckeditor5-react": "^11.0.0", "ckeditor5": "^46.0.3"}}