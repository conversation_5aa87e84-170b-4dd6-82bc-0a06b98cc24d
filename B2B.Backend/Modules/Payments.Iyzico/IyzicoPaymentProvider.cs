using System.Text.Json;
using Iyzipay;
using Iyzipay.Model;
using Iyzipay.Request;
using Payments.Abstractions;
using Payments.Abstractions.Models;

namespace Payments.Iyzico;

public class IyzicoPaymentProvider : IPaymentProvider
{
    private readonly Dictionary<string, string> _settings;

    public IyzicoPaymentProvider(Dictionary<string, string>? settings = null)
    {
        _settings = settings ?? new();
    }

    public async Task<CheckoutInitResult> InitializeCheckoutAsync(CheckoutInitData d, string ipAddress)
    {
        // Iyzipay options from settings
        var apiKey = _settings.GetValueOrDefault("ApiKey", string.Empty);
        var secretKey = _settings.GetValueOrDefault("SecretKey", string.Empty);
        var baseUrl = _settings.GetValueOrDefault("BaseUrl", "https://api.iyzipay.com");

        var iyzOpts = new Iyzipay.Options
        {
            ApiKey = apiKey,
            SecretKey = secretKey,
            BaseUrl = baseUrl
        };

        // Request
        var req = new CreateCheckoutFormInitializeRequest
        {
            Locale = Locale.TR.ToString(),
            ConversationId = Guid.NewGuid().ToString(),
            Price = PaymentFormat.ToIyzicoPrice(d.TotalPrice),
            PaidPrice = PaymentFormat.ToIyzicoPrice(d.TotalPrice),
            Currency = Currency.TRY.ToString(),
            BasketId = string.IsNullOrWhiteSpace(d.BasketId) ? Guid.NewGuid().ToString() : d.BasketId,
            PaymentGroup = PaymentGroup.PRODUCT.ToString(),
            CallbackUrl = d.CallbackUrl ?? d.CallbackOkUrl ?? d.CallbackFailUrl
        };

        // Buyer
        req.Buyer = new Buyer
        {
            Id = string.IsNullOrWhiteSpace(d.Buyer.Id) ? Guid.NewGuid().ToString() : d.Buyer.Id,
            Name = d.Buyer.Name,
            Surname = d.Buyer.Surname,
            Email = d.Buyer.Email,
            GsmNumber = d.Buyer.Phone,
            IdentityNumber = string.IsNullOrWhiteSpace(d.Buyer.IdentityNumber) ? "11111111111" : d.Buyer.IdentityNumber,
            RegistrationAddress = d.Ship.AddressText,
            City = d.Ship.City,
            Country = d.Ship.Country?.Length > 0 ? d.Ship.Country : "Turkey",
            Ip = string.IsNullOrWhiteSpace(ipAddress) ? "127.0.0.1" : ipAddress
        };

        // Addresses
        var shipping = new Iyzipay.Model.Address
        {
            City = d.Ship.City,
            Country = d.Ship.Country?.Length > 0 ? d.Ship.Country : "Turkey",
            ContactName = d.Ship.ContactName,
            Description = d.Ship.AddressText,
            ZipCode = d.Ship.PostalCode
        };
        req.ShippingAddress = shipping;

        var billing = new Iyzipay.Model.Address
        {
            City = d.Bill.City,
            Country = d.Bill.Country?.Length > 0 ? d.Bill.Country : "Turkey",
            ContactName = d.Bill.ContactName,
            Description = d.Bill.AddressText,
            ZipCode = d.Bill.PostalCode
        };
        req.BillingAddress = billing;

        // Basket items
        req.BasketItems = d.Items.Select((x, i) => new BasketItem
        {
            Id = x.Id,
            Name = x.Name,
            Category1 = x.Category1,
            ItemType = BasketItemType.PHYSICAL.ToString(),
            Price = PaymentFormat.ToIyzicoPrice(x.Price)
        }).ToList();

        // Iyzipay SDK call (sync API; wrap in Task.Run to avoid blocking)
        Console.WriteLine($"[IYZICO] Initialize request: {JsonSerializer.Serialize(req)}");
        var init = await Task.Run(() => CheckoutFormInitialize.Create(req, iyzOpts));
        Console.WriteLine($"[IYZICO] Initialize result: {init}");
        var success = string.Equals(init.Status, "success", StringComparison.OrdinalIgnoreCase);
        return new CheckoutInitResult
        {
            IsSuccess = success,
            Message = success ? "Success" : init.ErrorMessage ?? init.ErrorCode,
            Token = init?.Token ?? string.Empty,
            HtmlContent = init?.CheckoutFormContent,
            PaymentPageUrl = null,
            Raw = init
        };
    }

    public async Task<PaymentCallbackResponse> ValidateCallbackAsync(string token)
    {
        var apiKey = _settings.GetValueOrDefault("ApiKey", string.Empty);
        var secretKey = _settings.GetValueOrDefault("SecretKey", string.Empty);
        var baseUrl = _settings.GetValueOrDefault("BaseUrl", "https://sandbox-api.iyzipay.com");

        var iyzOpts = new Iyzipay.Options { ApiKey = apiKey, SecretKey = secretKey, BaseUrl = baseUrl };
        var req = new RetrieveCheckoutFormRequest { Token = token };
        var form = await Task.Run(() => CheckoutForm.Retrieve(req, iyzOpts));

        var success = string.Equals(form.Status, "success", StringComparison.OrdinalIgnoreCase);
        var message = success ? form?.PaymentStatus : form?.ErrorMessage ?? form?.ErrorCode;
        return new PaymentCallbackResponse
        {
            IsSuccess = success,
            Message = message,
            PaymentId = form?.PaymentId,
            LastFourDigits = form?.LastFourDigits
        };
    }
}
